<template>
  <div class="space-y-6">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-3 text-gray-600">Loading business information...</span>
    </div>

    <!-- No Business State -->
    <div v-else-if="!business" class="text-center py-12">
      <BuildingOfficeIcon class="h-16 w-16 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">No Business Found</h3>
      <p class="text-gray-600 mb-6">
        You haven't created a business yet. Create your business to start managing orders, inventory, and more.
      </p>
      <button
        @click="openCreateModal"
        class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center mx-auto"
      >
        <PlusIcon class="h-5 w-5 mr-2" />
        Create Your Business
      </button>
    </div>

    <!-- Business Information -->
    <div v-else class="space-y-6">
      <!-- Business Header -->
      <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
        <div class="flex items-start justify-between">
          <div class="flex items-start space-x-4">
            <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center">
              <BuildingOfficeIcon class="h-8 w-8 text-white" />
            </div>
            <div>
              <h2 class="text-2xl font-bold text-gray-900">{{ business.name }}</h2>
              <p class="text-sm text-gray-600 mt-1">{{ business.business_type_display }}</p>
              <div class="flex items-center mt-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                      :class="getStatusBadgeClass(business.status)">
                  {{ business.status_display }}
                </span>
              </div>
            </div>
          </div>
          <button
            @click="openEditModal"
            class="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="Edit Business"
          >
            <PencilIcon class="h-5 w-5" />
          </button>
        </div>

        <!-- Business Details -->
        <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div v-if="business.business_phone" class="flex items-center text-sm text-gray-600">
            <PhoneIcon class="h-4 w-4 mr-2" />
            {{ business.business_phone }}
          </div>
          <div v-if="business.business_email" class="flex items-center text-sm text-gray-600">
            <EnvelopeIcon class="h-4 w-4 mr-2" />
            {{ business.business_email }}
          </div>
          <div v-if="business.whatsapp_business_number" class="flex items-center text-sm text-gray-600">
            <ChatBubbleLeftRightIcon class="h-4 w-4 mr-2" />
            {{ business.whatsapp_business_number }}
          </div>
          <div v-if="business.address" class="flex items-start text-sm text-gray-600 md:col-span-2">
            <MapPinIcon class="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
            {{ business.address }}
          </div>
        </div>

        <!-- Description -->
        <div v-if="business.description" class="mt-4">
          <h4 class="text-sm font-medium text-gray-900 mb-2">Description</h4>
          <p class="text-sm text-gray-600">{{ business.description }}</p>
        </div>
      </div>

      <!-- Business Capabilities -->
      <div v-if="capabilities" class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Business Capabilities</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div v-for="(value, key) in capabilities.capabilities" :key="key" class="flex items-center">
            <CheckCircleIcon v-if="value" class="h-5 w-5 text-green-500 mr-2" />
            <XCircleIcon v-else class="h-5 w-5 text-gray-300 mr-2" />
            <span class="text-sm" :class="value ? 'text-gray-900' : 'text-gray-400'">
              {{ formatCapabilityName(key) }}
            </span>
          </div>
        </div>

        <!-- Features -->
        <div v-if="capabilities.features && capabilities.features.length > 0" class="mt-6">
          <h4 class="text-sm font-medium text-gray-900 mb-3">Available Features</h4>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="feature in capabilities.features"
              :key="feature"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              {{ formatFeatureName(feature) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Total Orders</p>
              <p class="text-2xl font-bold text-gray-900">{{ business.total_orders || 0 }}</p>
            </div>
            <ClipboardDocumentListIcon class="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Total Revenue</p>
              <p class="text-2xl font-bold text-gray-900">R{{ (business.total_revenue || 0).toLocaleString() }}</p>
            </div>
            <CurrencyDollarIcon class="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Total Products</p>
              <p class="text-2xl font-bold text-gray-900">{{ business.total_products || 0 }}</p>
            </div>
            <ArchiveBoxIcon class="h-8 w-8 text-orange-600" />
          </div>
        </div>
      </div>
    </div>

    <!-- Business Management Modal -->
    <BusinessManagementModal
      :is-open="showModal"
      :business="modalBusiness"
      @close="closeModal"
      @success="handleBusinessSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { businessService } from '@/services/podApi'
import BusinessManagementModal from './BusinessManagementModal.vue'
import {
  BuildingOfficeIcon,
  PlusIcon,
  PencilIcon,
  PhoneIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  MapPinIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  ArchiveBoxIcon
} from '@heroicons/vue/24/outline'

// State
const isLoading = ref(false)
const business = ref(null)
const capabilities = ref(null)
const showModal = ref(false)
const modalBusiness = ref(null)

// Methods
const loadBusiness = async () => {
  isLoading.value = true
  try {
    business.value = await businessService.getBusiness()
    if (business.value) {
      capabilities.value = await businessService.getBusinessCapabilities()
    }
  } catch (error) {
    console.error('Failed to load business:', error)
  } finally {
    isLoading.value = false
  }
}

const openCreateModal = () => {
  modalBusiness.value = null
  showModal.value = true
}

const openEditModal = () => {
  modalBusiness.value = business.value
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  modalBusiness.value = null
}

const handleBusinessSuccess = (newBusiness) => {
  business.value = newBusiness
  loadBusiness() // Reload to get updated capabilities
}

const getStatusBadgeClass = (status) => {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800'
    case 'inactive':
      return 'bg-gray-100 text-gray-800'
    case 'suspended':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const formatCapabilityName = (key) => {
  return key.replace(/supports_|_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatFeatureName = (feature) => {
  return feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

// Lifecycle
onMounted(() => {
  loadBusiness()
})
</script>
