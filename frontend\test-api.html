<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Mthunzi API Test</h1>
    
    <div class="test-section">
        <h2>Backend Connection Test</h2>
        <button onclick="testBackendConnection()">Test Backend Connection</button>
        <div id="backend-result"></div>
    </div>

    <div class="test-section">
        <h2>Authentication Test</h2>
        <input type="text" id="username" placeholder="Username" value="testuser">
        <input type="password" id="password" placeholder="Password" value="testpass">
        <button onclick="testLogin()">Test Login</button>
        <div id="auth-result"></div>
    </div>

    <div class="test-section">
        <h2>Business API Test</h2>
        <button onclick="testGetBusiness()">Get Business</button>
        <button onclick="testCreateBusiness()">Create Test Business</button>
        <div id="business-result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api';
        let authToken = '';

        function displayResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<pre>${message}</pre>`;
            element.className = isSuccess ? 'success' : 'error';
        }

        async function testBackendConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/auth/`, {
                    method: 'GET',
                });
                
                if (response.ok) {
                    displayResult('backend-result', 'Backend connection successful!', true);
                } else {
                    displayResult('backend-result', `Backend connection failed: ${response.status}`, false);
                }
            } catch (error) {
                displayResult('backend-result', `Backend connection error: ${error.message}`, false);
            }
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch(`${API_BASE_URL}/auth/login/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.token;
                    displayResult('auth-result', `Login successful!\nToken: ${data.token}\nUser: ${JSON.stringify(data.user, null, 2)}`, true);
                } else {
                    displayResult('auth-result', `Login failed: ${JSON.stringify(data, null, 2)}`, false);
                }
            } catch (error) {
                displayResult('auth-result', `Login error: ${error.message}`, false);
            }
        }

        async function testGetBusiness() {
            if (!authToken) {
                displayResult('business-result', 'Please login first to test business API', false);
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/auth/businesses/`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Token ${authToken}`,
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    displayResult('business-result', `Get Business successful!\n${JSON.stringify(data, null, 2)}`, true);
                } else {
                    displayResult('business-result', `Get Business failed: ${JSON.stringify(data, null, 2)}`, false);
                }
            } catch (error) {
                displayResult('business-result', `Get Business error: ${error.message}`, false);
            }
        }

        async function testCreateBusiness() {
            if (!authToken) {
                displayResult('business-result', 'Please login first to test business API', false);
                return;
            }

            const businessData = {
                name: 'Test Business',
                business_type: 'product_goods',
                business_phone: '+***********',
                business_email: '<EMAIL>',
                address: '123 Test Street, Test City',
                description: 'This is a test business created via API test'
            };

            try {
                const response = await fetch(`${API_BASE_URL}/auth/businesses/`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Token ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(businessData)
                });

                const data = await response.json();
                
                if (response.ok) {
                    displayResult('business-result', `Create Business successful!\n${JSON.stringify(data, null, 2)}`, true);
                } else {
                    displayResult('business-result', `Create Business failed: ${JSON.stringify(data, null, 2)}`, false);
                }
            } catch (error) {
                displayResult('business-result', `Create Business error: ${error.message}`, false);
            }
        }

        // Test backend connection on page load
        window.onload = function() {
            testBackendConnection();
        };
    </script>
</body>
</html>
