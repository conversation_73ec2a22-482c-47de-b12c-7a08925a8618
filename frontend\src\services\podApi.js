/**
 * API service functions for Pod dashboard operations
 */

const API_BASE_URL = 'http://localhost:8000/api'

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('authToken')
  return {
    'Content-Type': 'application/json',
    'Authorization': token ? `Token ${token}` : ''
  }
}

// Helper function to get auth headers for file uploads
const getFileUploadHeaders = () => {
  const token = localStorage.getItem('authToken')
  return {
    'Authorization': token ? `Token ${token}` : ''
  }
}

// Helper function to handle API responses
const handleResponse = async (response) => {
  if (!response.ok) {
    const error = await response.json().catch(() => ({ message: 'Network error' }))
    throw new Error(error.message || `HTTP error! status: ${response.status}`)
  }
  return response.json()
}

// Authentication Services
export const authService = {
  // Logout user
  logout: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/logout/`, {
        method: 'POST',
        headers: getAuthHeaders()
      })
      
      // Clear local storage regardless of response
      localStorage.removeItem('authToken')
      localStorage.removeItem('userRole')
      localStorage.removeItem('userData')
      
      return { success: true }
    } catch (error) {
      // Still clear local storage on error
      localStorage.removeItem('authToken')
      localStorage.removeItem('userRole')
      localStorage.removeItem('userData')
      throw error
    }
  }
}

// Inventory Services
export const inventoryService = {
  // Get all products for the business
  getProducts: async () => {
    const response = await fetch(`${API_BASE_URL}/orders/products/`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Add new product
  addProduct: async (productData) => {
    const response = await fetch(`${API_BASE_URL}/orders/products/`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(productData)
    })
    return handleResponse(response)
  },

  // Update product
  updateProduct: async (productId, productData) => {
    const response = await fetch(`${API_BASE_URL}/orders/products/${productId}/`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(productData)
    })
    return handleResponse(response)
  },

  // Delete product
  deleteProduct: async (productId) => {
    const response = await fetch(`${API_BASE_URL}/orders/products/${productId}/`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    })
    return response.ok
  },

  // Upload product image
  uploadProductImage: async (productId, imageFile) => {
    const formData = new FormData()
    formData.append('image', imageFile)
    
    const response = await fetch(`${API_BASE_URL}/orders/products/${productId}/upload-image/`, {
      method: 'POST',
      headers: getFileUploadHeaders(),
      body: formData
    })
    return handleResponse(response)
  },

  // Get inventory alerts
  getInventoryAlerts: async () => {
    const response = await fetch(`${API_BASE_URL}/auth/dashboard/inventory/`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  }
}

// Order Services
export const orderService = {
  // Get all orders
  getOrders: async (status = null) => {
    let url = `${API_BASE_URL}/orders/`
    if (status) {
      url += `?status=${status}`
    }
    
    const response = await fetch(url, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Get order details
  getOrderDetails: async (orderId) => {
    const response = await fetch(`${API_BASE_URL}/orders/${orderId}/`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Accept/Confirm order
  acceptOrder: async (orderId) => {
    const response = await fetch(`${API_BASE_URL}/orders/${orderId}/confirm/`, {
      method: 'POST',
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Cancel order
  cancelOrder: async (orderId, reason = '') => {
    const response = await fetch(`${API_BASE_URL}/orders/${orderId}/`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        status: 'cancelled',
        cancellation_reason: reason
      })
    })
    return handleResponse(response)
  },

  // Update order status
  updateOrderStatus: async (orderId, status, notes = '') => {
    const response = await fetch(`${API_BASE_URL}/orders/${orderId}/`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify({
        status: status,
        notes: notes
      })
    })
    return handleResponse(response)
  }
}

// Dashboard Services
export const dashboardService = {
  // Get business dashboard data
  getBusinessDashboard: async () => {
    const response = await fetch(`${API_BASE_URL}/auth/dashboard/business/`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Get sales analytics
  getSalesAnalytics: async (days = 30) => {
    const response = await fetch(`${API_BASE_URL}/auth/dashboard/sales/?days=${days}`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Get income statement data
  getIncomeStatement: async (startDate, endDate) => {
    let url = `${API_BASE_URL}/auth/dashboard/financial/`
    const params = new URLSearchParams()
    
    if (startDate) params.append('start_date', startDate)
    if (endDate) params.append('end_date', endDate)
    
    if (params.toString()) {
      url += `?${params.toString()}`
    }
    
    const response = await fetch(url, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  }
}

// Payment Services
export const paymentService = {
  // Get payment methods
  getPaymentMethods: async () => {
    const response = await fetch(`${API_BASE_URL}/payments/methods/`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  },

  // Get payments
  getPayments: async () => {
    const response = await fetch(`${API_BASE_URL}/payments/`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  }
}

// Business Management Services
export const businessService = {
  // Get current user's business
  getBusiness: async () => {
    const response = await fetch(`${API_BASE_URL}/auth/businesses/`, {
      headers: getAuthHeaders()
    })
    const data = await handleResponse(response)
    return data.length > 0 ? data[0] : null
  },

  // Create a new business
  createBusiness: async (businessData) => {
    const response = await fetch(`${API_BASE_URL}/auth/businesses/`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(businessData)
    })
    return handleResponse(response)
  },

  // Update business
  updateBusiness: async (businessId, businessData) => {
    const response = await fetch(`${API_BASE_URL}/auth/businesses/${businessId}/`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(businessData)
    })
    return handleResponse(response)
  },

  // Get business capabilities
  getBusinessCapabilities: async () => {
    const response = await fetch(`${API_BASE_URL}/auth/capabilities/`, {
      headers: getAuthHeaders()
    })
    return handleResponse(response)
  }
}

// File Upload Services
export const fileService = {
  // Upload multiple images
  uploadImages: async (files) => {
    const formData = new FormData()
    files.forEach((file, index) => {
      formData.append(`image_${index}`, file)
    })

    const response = await fetch(`${API_BASE_URL}/upload/images/`, {
      method: 'POST',
      headers: getFileUploadHeaders(),
      body: formData
    })
    return handleResponse(response)
  },

  // Delete image
  deleteImage: async (imageId) => {
    const response = await fetch(`${API_BASE_URL}/upload/images/${imageId}/`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    })
    return response.ok
  }
}

// Error handling utility
export const handleApiError = (error) => {
  console.error('API Error:', error)
  
  if (error.message.includes('401') || error.message.includes('Unauthorized')) {
    // Token expired or invalid
    localStorage.removeItem('authToken')
    localStorage.removeItem('userRole')
    localStorage.removeItem('userData')
    window.location.href = '/login'
    return
  }
  
  // Return user-friendly error message
  return error.message || 'An unexpected error occurred'
}
