<template>
  <div class="fixed top-4 right-4 z-50 space-y-2 w-fit">
    <transition-group
      name="toast"
      tag="div"
      class="space-y-2"
    >
      <div
        v-for="notification in notifications"
        :key="notification.id"
        :class="getNotificationClasses(notification.type)"
        class="flex w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
      >
        <div class="p-4">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <!-- Icon based on type -->
              <div :class="getIconClasses(notification.type)" class="w-8 h-8 rounded-full flex items-center justify-center">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path 
                    v-if="notification.type === 'success'" 
                    stroke-linecap="round" 
                    stroke-linejoin="round" 
                    stroke-width="2" 
                    d="M5 13l4 4L19 7"
                  />
                  <path 
                    v-else-if="notification.type === 'error'" 
                    stroke-linecap="round" 
                    stroke-linejoin="round" 
                    stroke-width="2" 
                    d="M6 18L18 6M6 6l12 12"
                  />
                  <path 
                    v-else-if="notification.type === 'warning'" 
                    stroke-linecap="round" 
                    stroke-linejoin="round" 
                    stroke-width="2" 
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                  <path 
                    v-else 
                    stroke-linecap="round" 
                    stroke-linejoin="round" 
                    stroke-width="2" 
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
            </div>
            
            <div class="ml-3 w-0 flex-1 pt-0.5 text-nowrap">
              <p class="text-sm font-medium text-[#111111]">{{ notification.title }}</p>
              <p v-if="notification.message" class="mt-1 text-sm text-[#4B4B4B]">{{ notification.message }}</p>
            </div>
            
            <div class="ml-4 flex-shrink-0 flex">
              <button
                @click="removeNotification(notification.id)"
                class="bg-white rounded-md inline-flex text-[#4B4B4B] hover:text-[#111111] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1E4E79]"
              >
                <span class="sr-only">Close</span>
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
        
        <!-- Progress bar for auto-dismiss -->
        <div 
          v-if="notification.autoDismiss && notification.duration"
          class="h-1 bg-gray-200"
        >
          <div 
            :class="getProgressBarClasses(notification.type)"
            class="h-full transition-all ease-linear"
            :style="{ width: `${notification.progress}%` }"
          ></div>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// State
const notifications = ref([])
let notificationId = 0

// Methods
const addNotification = (notification) => {
  const id = ++notificationId
  const newNotification = {
    id,
    type: notification.type || 'info',
    title: notification.title,
    message: notification.message,
    duration: notification.duration || 5000,
    autoDismiss: notification.autoDismiss !== false,
    progress: 100
  }
  
  notifications.value.push(newNotification)
  
  if (newNotification.autoDismiss) {
    startProgressTimer(newNotification)
  }
  
  return id
}

const removeNotification = (id) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

const startProgressTimer = (notification) => {
  const startTime = Date.now()
  const duration = notification.duration
  
  const updateProgress = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.max(0, 100 - (elapsed / duration) * 100)
    
    notification.progress = progress
    
    if (progress > 0) {
      requestAnimationFrame(updateProgress)
    } else {
      removeNotification(notification.id)
    }
  }
  
  requestAnimationFrame(updateProgress)
}

const getNotificationClasses = (type) => {
  switch (type) {
    case 'success':
      return 'border-l-4 border-[#2E7D32]'
    case 'error':
      return 'border-l-4 border-[#C62828]'
    case 'warning':
      return 'border-l-4 border-[#FF8F00]'
    default:
      return 'border-l-4 border-[#1E4E79]'
  }
}

const getIconClasses = (type) => {
  switch (type) {
    case 'success':
      return 'bg-[#2E7D32]/10 text-[#2E7D32]'
    case 'error':
      return 'bg-[#C62828]/10 text-[#C62828]'
    case 'warning':
      return 'bg-[#FF8F00]/10 text-[#FF8F00]'
    default:
      return 'bg-[#1E4E79]/10 text-[#1E4E79]'
  }
}

const getProgressBarClasses = (type) => {
  switch (type) {
    case 'success':
      return 'bg-[#2E7D32]'
    case 'error':
      return 'bg-[#C62828]'
    case 'warning':
      return 'bg-[#FF8F00]'
    default:
      return 'bg-[#1E4E79]'
  }
}

// Expose methods for external use
const showSuccess = (title, message, options = {}) => {
  return addNotification({ type: 'success', title, message, ...options })
}

const showError = (title, message, options = {}) => {
  return addNotification({ type: 'error', title, message, ...options })
}

const showWarning = (title, message, options = {}) => {
  return addNotification({ type: 'warning', title, message, ...options })
}

const showInfo = (title, message, options = {}) => {
  return addNotification({ type: 'info', title, message, ...options })
}

// Global notification service
const notificationService = {
  success: showSuccess,
  error: showError,
  warning: showWarning,
  info: showInfo,
  remove: removeNotification
}

// Make it available globally
if (typeof window !== 'undefined') {
  window.$notify = notificationService
}

// Expose for component usage
defineExpose({
  showSuccess,
  showError,
  showWarning,
  showInfo,
  removeNotification,
  notifications
})
</script>

<style scoped>
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.toast-move {
  transition: transform 0.3s ease;
}
</style>
