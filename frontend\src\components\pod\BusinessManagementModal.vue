<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 class="text-xl font-bold text-gray-900">
          {{ business ? 'Manage Business' : 'Create Your Business' }}
        </h2>
        <button @click="closeModal" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
          <XMarkIcon class="h-5 w-5 text-gray-500" />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6">
        <form @submit.prevent="handleSubmit">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Business Name -->
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-2">Business Name *</label>
              <input
                v-model="formData.name"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
                placeholder="Enter your business name"
              >
            </div>

            <!-- Business Type -->
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-2">Business Type *</label>
              <select
                v-model="formData.business_type"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Select business type</option>
                <option value="product_goods">Product & Goods</option>
                <option value="service">Service Business</option>
                <option value="food_restaurant">Food & Restaurant</option>
              </select>
            </div>

            <!-- Business Phone -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Business Phone</label>
              <input
                v-model="formData.business_phone"
                type="tel"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="+***********"
              >
            </div>

            <!-- Business Email -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Business Email</label>
              <input
                v-model="formData.business_email"
                type="email"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="<EMAIL>"
              >
            </div>

            <!-- WhatsApp Business Number -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">WhatsApp Business Number</label>
              <input
                v-model="formData.whatsapp_business_number"
                type="tel"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="+***********"
              >
            </div>

            <!-- Address -->
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-2">Business Address</label>
              <textarea
                v-model="formData.address"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter your business address"
              ></textarea>
            </div>

            <!-- Description -->
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-2">Business Description</label>
              <textarea
                v-model="formData.description"
                rows="4"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Describe your business and what you offer"
              ></textarea>
            </div>

            <!-- Welcome Message -->
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-2">WhatsApp Welcome Message</label>
              <textarea
                v-model="formData.welcome_message"
                rows="4"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Welcome message for customers contacting you via WhatsApp"
              ></textarea>
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="errorMessage" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p class="text-sm text-red-600">{{ errorMessage }}</p>
          </div>

          <!-- Success Message -->
          <div v-if="successMessage" class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <p class="text-sm text-green-600">{{ successMessage }}</p>
          </div>

          <!-- Actions -->
          <div class="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="isLoading || !isFormValid"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div v-if="isLoading" class="flex items-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {{ business ? 'Updating...' : 'Creating...' }}
              </div>
              <div v-else class="flex items-center">
                <CheckIcon class="h-4 w-4 mr-2" />
                {{ business ? 'Update Business' : 'Create Business' }}
              </div>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { businessService } from '@/services/podApi'
import { XMarkIcon, CheckIcon } from '@heroicons/vue/24/outline'

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  business: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['close', 'success'])

// State
const isLoading = ref(false)
const errorMessage = ref('')
const successMessage = ref('')

const formData = ref({
  name: '',
  business_type: '',
  business_phone: '',
  business_email: '',
  address: '',
  description: '',
  whatsapp_business_number: '',
  welcome_message: ''
})

// Computed
const isFormValid = computed(() => {
  return formData.value.name && formData.value.business_type
})

// Watch for business changes to populate form
watch(() => props.business, (newBusiness) => {
  if (newBusiness) {
    formData.value = {
      name: newBusiness.name || '',
      business_type: newBusiness.business_type || '',
      business_phone: newBusiness.business_phone || '',
      business_email: newBusiness.business_email || '',
      address: newBusiness.address || '',
      description: newBusiness.description || '',
      whatsapp_business_number: newBusiness.whatsapp_business_number || '',
      welcome_message: newBusiness.welcome_message || ''
    }
  } else {
    // Reset form for new business
    formData.value = {
      name: '',
      business_type: '',
      business_phone: '',
      business_email: '',
      address: '',
      description: '',
      whatsapp_business_number: '',
      welcome_message: ''
    }
  }
  errorMessage.value = ''
  successMessage.value = ''
}, { immediate: true })

// Methods
const closeModal = () => {
  emit('close')
}

const handleSubmit = async () => {
  if (!isFormValid.value || isLoading.value) return

  isLoading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    let result
    if (props.business) {
      // Update existing business
      result = await businessService.updateBusiness(props.business.id, formData.value)
      successMessage.value = 'Business updated successfully!'
    } else {
      // Create new business
      result = await businessService.createBusiness(formData.value)
      successMessage.value = 'Business created successfully!'
    }

    // Emit success event with the result
    emit('success', result)

    // Close modal after a short delay
    setTimeout(() => {
      closeModal()
    }, 1500)

  } catch (error) {
    console.error('Business operation failed:', error)
    errorMessage.value = error.message || 'An error occurred. Please try again.'
  } finally {
    isLoading.value = false
  }
}
</script>
