<template>
  <div class="min-h-screen bg-gradient-to-br from-[#EDF6FF] via-[#F5F5F5] to-[#FFF1DB]">
    <!-- Header -->
    <header class="bg-[#FFFFFF] shadow-sm border-b border-[#CCCCCC]/30">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo & Title -->
          <div class="flex items-center space-x-4">
            <div
              class="w-10 h-10 bg-gradient-to-br from-[#C1843E] to-[#704A1F] rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div>
              <h1 class="text-xl font-bold text-[#111111]">{{ businessName }}</h1>
              <p class="text-sm text-[#4B4B4B]">Pod Dashboard</p>
            </div>
          </div>

          <!-- Navigation & User Menu -->
          <div class="flex items-center space-x-6">
            <nav class="hidden md:flex space-x-6">
              <button @click="activeTab = 'overview'"
                :class="activeTab === 'overview' ? 'text-[#1E4E79] border-b-2 border-[#1E4E79]' : 'text-[#4B4B4B] hover:text-[#1E4E79]'"
                class="pb-1 transition-colors duration-300">
                Overview
              </button>
              <button @click="activeTab = 'business'"
                :class="activeTab === 'business' ? 'text-[#1E4E79] border-b-2 border-[#1E4E79]' : 'text-[#4B4B4B] hover:text-[#1E4E79]'"
                class="pb-1 transition-colors duration-300">
                Business
              </button>
              <button @click="activeTab = 'orders'"
                :class="activeTab === 'orders' ? 'text-[#1E4E79] border-b-2 border-[#1E4E79]' : 'text-[#4B4B4B] hover:text-[#1E4E79]'"
                class="pb-1 transition-colors duration-300">
                Orders
              </button>
              <button @click="activeTab = 'inventory'"
                :class="activeTab === 'inventory' ? 'text-[#1E4E79] border-b-2 border-[#1E4E79]' : 'text-[#4B4B4B] hover:text-[#1E4E79]'"
                class="pb-1 transition-colors duration-300">
                Inventory
              </button>
              <button @click="activeTab = 'reports'"
                :class="activeTab === 'reports' ? 'text-[#1E4E79] border-b-2 border-[#1E4E79]' : 'text-[#4B4B4B] hover:text-[#1E4E79]'"
                class="pb-1 transition-colors duration-300">
                Reports
              </button>
              <button @click="activeTab = 'income'"
                :class="activeTab === 'income' ? 'text-[#1E4E79] border-b-2 border-[#1E4E79]' : 'text-[#4B4B4B] hover:text-[#1E4E79]'"
                class="pb-1 transition-colors duration-300">
                Income
              </button>
            </nav>

            <!-- User Menu Dropdown -->
            <div class="relative" ref="userMenuRef">
              <button @click="showUserMenu = !showUserMenu"
                class="flex items-center space-x-2 p-2 rounded-xl hover:bg-[#F5F5F5] transition-colors duration-300">
                <div
                  class="w-8 h-8 bg-gradient-to-br from-[#1E4E79] to-[#132F4C] rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-semibold">P</span>
                </div>
                <svg class="w-4 h-4 text-[#4B4B4B] transition-transform duration-300"
                  :class="{ 'rotate-180': showUserMenu }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              <!-- Dropdown Menu -->
              <div v-if="showUserMenu"
                class="absolute right-0 mt-2 w-48 bg-[#FFFFFF] rounded-xl shadow-lg border border-[#CCCCCC]/30 py-2 z-50">
                <div class="px-4 py-2 border-b border-[#CCCCCC]/30">
                  <p class="text-sm font-medium text-[#111111]">{{ businessName }}</p>
                  <p class="text-xs text-[#4B4B4B]">Pod Account</p>
                </div>
                <button @click="handleLogout" :disabled="isLoggingOut"
                  class="w-full text-left px-4 py-2 text-sm text-[#C62828] hover:bg-[#F5F5F5] transition-colors duration-300 flex items-center space-x-2">
                  <svg v-if="isLoggingOut" class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                  </svg>
                  <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  <span>{{ isLoggingOut ? 'Signing out...' : 'Sign out' }}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Error Message -->
    <div v-if="errorMessage" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
      <div class="bg-red-50 border border-red-200 rounded-xl p-4 flex items-center justify-between">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p class="text-sm text-red-700">{{ errorMessage }}</p>
        </div>
        <button @click="clearError" class="text-red-500 hover:text-red-700 transition-colors duration-200">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Overview Tab -->
      <div v-if="activeTab === 'overview'">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div
            class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">Today's Orders</p>
                <p class="text-3xl font-bold text-[#111111]">{{ stats.todayOrders }}</p>
                <p class="text-sm text-[#2E7D32]">+12% from yesterday</p>
              </div>
              <div class="w-12 h-12 bg-[#2E7D32]/10 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-[#2E7D32]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
            </div>
          </div>

          <div
            class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">Pending Payments</p>
                <p class="text-3xl font-bold text-[#111111]">{{ stats.pendingPayments }}</p>
                <p class="text-sm text-[#FF8F00]">Requires attention</p>
              </div>
              <div class="w-12 h-12 bg-[#FF8F00]/10 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-[#FF8F00]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </div>

          <div
            class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">Monthly Revenue</p>
                <p class="text-3xl font-bold text-[#111111]">R{{ stats.monthlyRevenue.toLocaleString() }}</p>
                <p class="text-sm text-[#2E7D32]">+8% from last month</p>
              </div>
              <div class="w-12 h-12 bg-[#C1843E]/10 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-[#C1843E]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
          </div>

          <div
            class="bg-[#FFFFFF] rounded-2xl p-6 shadow-sm border border-[#CCCCCC]/30 hover:shadow-md transition-all duration-300">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-[#4B4B4B]">Low Stock Items</p>
                <p class="text-3xl font-bold text-[#111111]">{{ stats.lowStockItems }}</p>
                <p class="text-sm text-[#C62828]">Need restocking</p>
              </div>
              <div class="w-12 h-12 bg-[#C62828]/10 rounded-xl flex items-center justify-center">
                <svg class="w-6 h-6 text-[#C62828]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Orders -->
        <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 mb-8">
          <div class="p-6 border-b border-[#CCCCCC]/30">
            <h2 class="text-lg font-semibold text-[#111111]">Recent Orders</h2>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <div v-for="order in recentOrders" :key="order.id"
                class="flex items-center justify-between p-4 bg-[#F5F5F5] rounded-xl hover:bg-[#EDF6FF] transition-colors duration-200">
                <div class="flex items-center space-x-4">
                  <div
                    class="w-10 h-10 bg-gradient-to-br from-[#1E4E79] to-[#132F4C] rounded-xl flex items-center justify-center">
                    <span class="text-white font-semibold text-sm">#{{ order.id }}</span>
                  </div>
                  <div>
                    <p class="font-medium text-[#111111]">{{ order.customer }}</p>
                    <p class="text-sm text-[#4B4B4B]">{{ order.items }} items • {{ order.time }}</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="font-semibold text-[#111111]">R{{ order.amount }}</p>
                  <span :class="getOrderStatusClass(order.status)" class="px-2 py-1 rounded-full text-xs font-medium">
                    {{ order.status }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Business Tab -->
      <div v-if="activeTab === 'business'">
        <BusinessOverview />
      </div>

      <!-- Orders Tab -->
      <div v-if="activeTab === 'orders'">
        <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30">
          <div class="p-6 border-b border-[#CCCCCC]/30">
            <div class="flex justify-between items-center">
              <h2 class="text-lg font-semibold text-[#111111]">All Orders</h2>
              <div class="flex space-x-2">
                <select
                  class="px-3 py-2 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] focus:ring-2 focus:ring-[#1E4E79]/20">
                  <option>All Status</option>
                  <option>Pending</option>
                  <option>Confirmed</option>
                  <option>Delivered</option>
                </select>
              </div>
            </div>
          </div>
          <div class="p-6">
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead>
                  <tr class="border-b border-[#CCCCCC]/30">
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Order ID</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Customer</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Items</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Amount</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Status</th>
                    <th class="text-left py-3 px-4 font-semibold text-[#111111]">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="order in allOrders" :key="order.id"
                    class="border-b border-[#CCCCCC]/20 hover:bg-[#F5F5F5] transition-colors duration-200">
                    <td class="py-4 px-4 font-medium text-[#111111]">#{{ order.id }}</td>
                    <td class="py-4 px-4 text-[#111111]">{{ order.customer }}</td>
                    <td class="py-4 px-4 text-[#4B4B4B]">{{ order.items }} items</td>
                    <td class="py-4 px-4 font-semibold text-[#111111]">R{{ order.amount }}</td>
                    <td class="py-4 px-4">
                      <span :class="getOrderStatusClass(order.status)"
                        class="px-3 py-1 rounded-full text-xs font-medium">
                        {{ order.status }}
                      </span>
                    </td>
                    <td class="py-4 px-4">
                      <div class="flex space-x-2">
                        <!-- View Order Details -->
                        <button @click="viewOrderDetails(order)"
                          class="p-2 text-[#1E4E79] hover:bg-[#1E4E79]/10 rounded-lg transition-colors duration-200"
                          title="View Details">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </button>

                        <!-- Accept Order (only for pending orders) -->
                        <button v-if="order.status === 'Pending'" @click="acceptOrder(order.id)"
                          :disabled="processingOrders.includes(order.id)"
                          class="p-2 text-[#2E7D32] hover:bg-[#2E7D32]/10 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Accept Order">
                          <svg v-if="processingOrders.includes(order.id)" class="w-4 h-4 animate-spin" fill="none"
                            viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                            </circle>
                            <path class="opacity-75" fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                            </path>
                          </svg>
                          <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                          </svg>
                        </button>

                        <!-- Cancel Order (only for pending/confirmed orders) -->
                        <button v-if="order.status === 'Pending' || order.status === 'Confirmed'"
                          @click="selectedOrder = order; showCancelOrderModal = true"
                          :disabled="processingOrders.includes(order.id)"
                          class="p-2 text-[#C62828] hover:bg-[#C62828]/10 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Cancel Order">
                          <svg v-if="processingOrders.includes(order.id)" class="w-4 h-4 animate-spin" fill="none"
                            viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                            </circle>
                            <path class="opacity-75" fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                            </path>
                          </svg>
                          <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Inventory Tab -->
      <div v-if="activeTab === 'inventory'">
        <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30">
          <div class="p-6 border-b border-[#CCCCCC]/30">
            <div class="flex justify-between items-center">
              <h2 class="text-lg font-semibold text-[#111111]">Inventory Management</h2>
              <button @click="showAddInventoryModal = true"
                class="bg-gradient-to-r from-[#1E4E79] to-[#132F4C] text-white px-4 py-2 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                Add Product
              </button>
            </div>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div v-for="product in inventory" :key="product.id"
                class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 overflow-hidden hover:shadow-md transition-all duration-300">
                <!-- Product Image -->
                <div class="relative h-48 bg-[#F5F5F5] overflow-hidden">
                  <img v-if="product.images && product.images.length > 0" :src="product.images[0]" :alt="product.name"
                    class="w-full h-full object-cover" />
                  <div v-else class="w-full h-full flex items-center justify-center">
                    <svg class="w-16 h-16 text-[#CCCCCC]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>

                  <!-- Image Count Badge -->
                  <div v-if="product.images && product.images.length > 1"
                    class="absolute top-2 right-2 bg-[#111111]/70 text-white text-xs px-2 py-1 rounded-full">
                    +{{ product.images.length - 1 }}
                  </div>

                  <!-- Stock Status Badge -->
                  <div class="absolute top-2 left-2">
                    <span :class="getStockStatusClass(product.stock)"
                      class="px-2 py-1 rounded-full text-xs font-medium">
                      {{ getStockStatus(product.stock) }}
                    </span>
                  </div>
                </div>

                <!-- Product Details -->
                <div class="p-4">
                  <div class="flex items-center justify-between mb-3">
                    <h3 class="font-semibold text-[#111111] truncate">{{ product.name }}</h3>
                    <button @click="uploadProductImages(product)"
                      class="p-1 text-[#4B4B4B] hover:text-[#1E4E79] transition-colors duration-300"
                      title="Upload Images">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                      </svg>
                    </button>
                  </div>

                  <div class="space-y-2 mb-4">
                    <div class="flex justify-between">
                      <span class="text-[#4B4B4B] text-sm">Price:</span>
                      <span class="font-medium text-[#111111]">R{{ product.price }}</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-[#4B4B4B] text-sm">Stock:</span>
                      <span class="font-medium text-[#111111]">{{ product.stock }} units</span>
                    </div>
                    <div class="flex justify-between">
                      <span class="text-[#4B4B4B] text-sm">Sold:</span>
                      <span class="font-medium text-[#111111]">{{ product.sold }} units</span>
                    </div>
                  </div>

                  <div class="flex space-x-2">
                    <button @click="editProduct(product)"
                      class="flex-1 bg-[#1E4E79] text-white py-2 px-3 rounded-xl hover:bg-[#132F4C] transition-colors duration-300 text-sm">
                      Edit
                    </button>
                    <button @click="restockProduct(product)"
                      class="flex-1 bg-[#C1843E] text-white py-2 px-3 rounded-xl hover:bg-[#704A1F] transition-colors duration-300 text-sm">
                      Restock
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Reports Tab -->
      <div v-if="activeTab === 'reports'">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 p-6">
            <h3 class="text-lg font-semibold text-[#111111] mb-4">Sales Report</h3>
            <div class="h-64 bg-[#F5F5F5] rounded-xl flex items-center justify-center">
              <p class="text-[#4B4B4B]">Sales Chart Placeholder</p>
            </div>
          </div>

          <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 p-6">
            <h3 class="text-lg font-semibold text-[#111111] mb-4">Top Products</h3>
            <div class="space-y-4">
              <div v-for="product in topProducts" :key="product.id"
                class="flex items-center justify-between p-3 bg-[#F5F5F5] rounded-xl">
                <div>
                  <p class="font-medium text-[#111111]">{{ product.name }}</p>
                  <p class="text-sm text-[#4B4B4B]">{{ product.sold }} sold</p>
                </div>
                <div class="text-right">
                  <p class="font-semibold text-[#111111]">R{{ product.revenue.toLocaleString() }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Income Statement Tab -->
      <div v-if="activeTab === 'income'">
        <div class="space-y-6">
          <!-- Income Statement Header -->
          <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 p-6">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-xl font-bold text-[#111111]">Income Statement</h2>
              <div class="flex space-x-3">
                <select v-model="incomeStatementPeriod" @change="loadIncomeStatement"
                  class="px-3 py-2 bg-[#F5F5F5] border-0 rounded-xl text-[#111111] focus:ring-2 focus:ring-[#1E4E79]/20">
                  <option value="30">Last 30 Days</option>
                  <option value="90">Last 3 Months</option>
                  <option value="365">Last Year</option>
                  <option value="custom">Custom Range</option>
                </select>
                <button @click="exportIncomeStatement"
                  class="px-4 py-2 bg-[#C1843E] text-white rounded-xl hover:bg-[#704A1F] transition-colors duration-300">
                  Export PDF
                </button>
              </div>
            </div>

            <!-- Financial Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div
                class="bg-gradient-to-br from-[#2E7D32]/10 to-[#2E7D32]/5 rounded-2xl p-6 border border-[#2E7D32]/20">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-[#2E7D32]">Total Revenue</p>
                    <p class="text-2xl font-bold text-[#2E7D32]">R{{ incomeData.totalRevenue.toLocaleString() }}</p>
                    <p class="text-xs text-[#2E7D32]/70">{{ incomeStatementPeriod }} days</p>
                  </div>
                  <div class="w-12 h-12 bg-[#2E7D32]/20 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-[#2E7D32]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                </div>
              </div>

              <div
                class="bg-gradient-to-br from-[#FF8F00]/10 to-[#FF8F00]/5 rounded-2xl p-6 border border-[#FF8F00]/20">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-[#FF8F00]">Total Expenses</p>
                    <p class="text-2xl font-bold text-[#FF8F00]">R{{ incomeData.totalExpenses.toLocaleString() }}</p>
                    <p class="text-xs text-[#FF8F00]/70">{{ incomeStatementPeriod }} days</p>
                  </div>
                  <div class="w-12 h-12 bg-[#FF8F00]/20 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-[#FF8F00]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                  </div>
                </div>
              </div>

              <div
                class="bg-gradient-to-br from-[#1E4E79]/10 to-[#1E4E79]/5 rounded-2xl p-6 border border-[#1E4E79]/20">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-[#1E4E79]">Net Profit</p>
                    <p class="text-2xl font-bold"
                      :class="incomeData.netProfit >= 0 ? 'text-[#2E7D32]' : 'text-[#C62828]'">
                      R{{ incomeData.netProfit.toLocaleString() }}
                    </p>
                    <p class="text-xs text-[#1E4E79]/70">{{ ((incomeData.netProfit / incomeData.totalRevenue) *
                      100).toFixed(1) }}% margin</p>
                  </div>
                  <div class="w-12 h-12 bg-[#1E4E79]/20 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-[#1E4E79]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Detailed Income Statement -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Revenue Breakdown -->
            <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 p-6">
              <h3 class="text-lg font-semibold text-[#111111] mb-4">Revenue Breakdown</h3>
              <div class="space-y-4">
                <div v-for="item in incomeData.revenueBreakdown" :key="item.category"
                  class="flex items-center justify-between p-3 bg-[#F5F5F5] rounded-xl">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: item.color }"></div>
                    <span class="font-medium text-[#111111]">{{ item.category }}</span>
                  </div>
                  <div class="text-right">
                    <p class="font-semibold text-[#111111]">R{{ item.amount.toLocaleString() }}</p>
                    <p class="text-xs text-[#4B4B4B]">{{ ((item.amount / incomeData.totalRevenue) * 100).toFixed(1) }}%
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Expense Breakdown -->
            <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 p-6">
              <h3 class="text-lg font-semibold text-[#111111] mb-4">Expense Breakdown</h3>
              <div class="space-y-4">
                <div v-for="item in incomeData.expenseBreakdown" :key="item.category"
                  class="flex items-center justify-between p-3 bg-[#F5F5F5] rounded-xl">
                  <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: item.color }"></div>
                    <span class="font-medium text-[#111111]">{{ item.category }}</span>
                  </div>
                  <div class="text-right">
                    <p class="font-semibold text-[#111111]">R{{ item.amount.toLocaleString() }}</p>
                    <p class="text-xs text-[#4B4B4B]">{{ ((item.amount / incomeData.totalExpenses) * 100).toFixed(1) }}%
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Monthly Trend Chart -->
          <div class="bg-[#FFFFFF] rounded-2xl shadow-sm border border-[#CCCCCC]/30 p-6">
            <h3 class="text-lg font-semibold text-[#111111] mb-4">Monthly Profit Trend</h3>
            <div class="h-64 bg-[#F5F5F5] rounded-xl flex items-center justify-center">
              <p class="text-[#4B4B4B]">Profit Trend Chart Placeholder</p>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Add Inventory Modal -->
    <AddInventoryModal :is-open="showAddInventoryModal" @close="showAddInventoryModal = false"
      @product-added="handleProductAdded" />

    <!-- Order Details Modal -->
    <OrderDetailsModal :is-open="showOrderDetailsModal" :order="selectedOrder"
      :is-processing="processingOrders.includes(selectedOrder?.id)" @close="showOrderDetailsModal = false"
      @accept-order="acceptOrderFromModal" @cancel-order="showCancelOrderModal = true" />

    <!-- Cancel Order Modal -->
    <CancelOrderModal :is-open="showCancelOrderModal" :order-id="selectedOrder?.id"
      :is-processing="processingOrders.includes(selectedOrder?.id)" @close="showCancelOrderModal = false"
      @confirm="cancelOrderFromModal" />

    <!-- Restock Modal -->
    <RestockModal :is-open="showRestockModal" :product="selectedProduct" :is-processing="isProcessingRestock"
      @close="showRestockModal = false" @confirm="restockProductFromModal" />

    <!-- Image Upload Modal -->
    <ImageUploadModal :is-open="showImageUploadModal" :product="selectedProduct" @close="showImageUploadModal = false"
      @images-updated="handleImagesUpdated" />

    <!-- Edit Product Modal -->
    <EditProductModal :is-open="showEditProductModal" :product="selectedProduct" @close="showEditProductModal = false"
      @product-updated="handleProductUpdated" @manage-images="openImageUploadFromEdit" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'
import { authService, orderService, dashboardService, inventoryService, businessService } from '../services/podApi.js'
import AddInventoryModal from '../components/pod/AddInventoryModal.vue'
import OrderDetailsModal from '../components/pod/OrderDetailsModal.vue'
import CancelOrderModal from '../components/pod/CancelOrderModal.vue'
import RestockModal from '../components/pod/RestockModal.vue'
import ImageUploadModal from '../components/pod/ImageUploadModal.vue'
import EditProductModal from '../components/pod/EditProductModal.vue'
import BusinessOverview from '../components/pod/BusinessOverview.vue'

const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const activeTab = ref('overview')
const businessName = ref('')
const showUserMenu = ref(false)
const isLoggingOut = ref(false)
const userMenuRef = ref(null)
const showAddInventoryModal = ref(false)
const processingOrders = ref([])
const incomeStatementPeriod = ref('30')

// Loading states
const isLoadingStats = ref(false)
const isLoadingOrders = ref(false)
const isLoadingInventory = ref(false)
const isLoadingIncome = ref(false)
const errorMessage = ref('')

// Modal states
const showOrderDetailsModal = ref(false)
const showCancelOrderModal = ref(false)
const showRestockModal = ref(false)
const showImageUploadModal = ref(false)
const showEditProductModal = ref(false)
const selectedOrder = ref(null)
const selectedProduct = ref(null)
const isProcessingRestock = ref(false)

// Dynamic data that will be loaded from API
const incomeData = ref({
  totalRevenue: 0,
  totalExpenses: 0,
  netProfit: 0,
  revenueBreakdown: [],
  expenseBreakdown: []
})

const stats = ref({
  todayOrders: 0,
  pendingPayments: 0,
  monthlyRevenue: 0,
  lowStockItems: 0
})

const recentOrders = ref([])
const allOrders = ref([])
const inventory = ref([])
const topProducts = ref([])

// Methods
const getOrderStatusClass = (status) => {
  switch (status) {
    case 'Pending':
      return 'bg-[#FF8F00]/10 text-[#FF8F00]'
    case 'Confirmed':
      return 'bg-[#1E4E79]/10 text-[#1E4E79]'
    case 'Delivered':
      return 'bg-[#2E7D32]/10 text-[#2E7D32]'
    case 'Cancelled':
      return 'bg-[#C62828]/10 text-[#C62828]'
    default:
      return 'bg-[#4B4B4B]/10 text-[#4B4B4B]'
  }
}

const getStockStatus = (stock) => {
  if (stock <= 10) return 'Low Stock'
  if (stock <= 25) return 'Medium'
  return 'In Stock'
}

const getStockStatusClass = (stock) => {
  if (stock <= 10) return 'bg-[#C62828]/10 text-[#C62828]'
  if (stock <= 25) return 'bg-[#FF8F00]/10 text-[#FF8F00]'
  return 'bg-[#2E7D32]/10 text-[#2E7D32]'
}

// Error handling helper
const handleApiError = (error, defaultMessage) => {
  console.error('API Error:', error)

  // Check for authentication errors
  if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
    console.log('Authentication error detected, logging out...')
    authStore.logout()
    router.push('/')
    return
  }

  // Check for permission errors
  if (error.message?.includes('403') || error.message?.includes('Forbidden')) {
    errorMessage.value = 'You do not have permission to access this resource'
    return
  }

  // Set user-friendly error message
  errorMessage.value = error.message || defaultMessage
}

// Data loading functions
const loadDashboardStats = async () => {
  isLoadingStats.value = true
  try {
    const data = await dashboardService.getBusinessDashboard()
    stats.value = {
      todayOrders: data.today_orders || 0,
      pendingPayments: data.pending_payments || 0,
      monthlyRevenue: data.monthly_revenue || 0,
      lowStockItems: data.low_stock_items || 0
    }

    // Set business name from user data or API response
    if (authStore.user?.business_name) {
      businessName.value = authStore.user.business_name
    } else if (data.business_name) {
      businessName.value = data.business_name
    }
  } catch (error) {
    handleApiError(error, 'Failed to load dashboard statistics')
  } finally {
    isLoadingStats.value = false
  }
}

const loadOrders = async () => {
  isLoadingOrders.value = true
  try {
    const data = await orderService.getOrders()
    allOrders.value = data.results || data || []

    // Get recent orders (last 5)
    recentOrders.value = allOrders.value
      .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
      .slice(0, 5)
      .map(order => ({
        ...order,
        time: formatTimeAgo(order.created_at)
      }))
  } catch (error) {
    handleApiError(error, 'Failed to load orders')
  } finally {
    isLoadingOrders.value = false
  }
}

const loadInventory = async () => {
  isLoadingInventory.value = true
  try {
    const data = await inventoryService.getProducts()
    inventory.value = data.results || data || []

    // Calculate top products based on sales
    topProducts.value = inventory.value
      .filter(product => product.sold > 0)
      .sort((a, b) => b.sold - a.sold)
      .slice(0, 5)
      .map(product => ({
        id: product.id,
        name: product.name,
        sold: product.sold || 0,
        revenue: (product.sold || 0) * (product.price || 0)
      }))
  } catch (error) {
    handleApiError(error, 'Failed to load inventory')
  } finally {
    isLoadingInventory.value = false
  }
}

const loadIncomeData = async () => {
  isLoadingIncome.value = true
  try {
    const data = await dashboardService.getSalesAnalytics(parseInt(incomeStatementPeriod.value))
    incomeData.value = {
      totalRevenue: data.total_revenue || 0,
      totalExpenses: data.total_expenses || 0,
      netProfit: (data.total_revenue || 0) - (data.total_expenses || 0),
      revenueBreakdown: data.revenue_breakdown || [],
      expenseBreakdown: data.expense_breakdown || []
    }
  } catch (error) {
    handleApiError(error, 'Failed to load income statement')
  } finally {
    isLoadingIncome.value = false
  }
}

// Helper function to format time ago
const formatTimeAgo = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInMinutes = Math.floor((now - date) / (1000 * 60))

  if (diffInMinutes < 60) {
    return `${diffInMinutes} minutes ago`
  } else if (diffInMinutes < 1440) {
    const hours = Math.floor(diffInMinutes / 60)
    return `${hours} hour${hours > 1 ? 's' : ''} ago`
  } else {
    const days = Math.floor(diffInMinutes / 1440)
    return `${days} day${days > 1 ? 's' : ''} ago`
  }
}

// Load all data
const loadAllData = async () => {
  await Promise.all([
    loadDashboardStats(),
    loadOrders(),
    loadInventory(),
    loadIncomeData()
  ])
}

// Clear error message
const clearError = () => {
  errorMessage.value = ''
}

// Logout functionality
const handleLogout = async () => {
  if (isLoggingOut.value) return

  isLoggingOut.value = true
  showUserMenu.value = false

  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('Logout error:', error)
    // Still redirect to login even if API call fails
    router.push('/')
  } finally {
    isLoggingOut.value = false
  }
}

// Computed properties
const lowStockProducts = computed(() => {
  return inventory.value.filter(product => product.stock <= (product.low_stock_threshold || 10))
})

const filteredOrders = computed(() => {
  return allOrders.value
})

const filteredInventory = computed(() => {
  return inventory.value
})

// Handle product added
const handleProductAdded = (newProduct) => {
  // Add the new product to the inventory list
  inventory.value.push({
    id: newProduct.id,
    name: newProduct.name,
    price: newProduct.price,
    stock: newProduct.stock_quantity,
    sold: 0 // New products start with 0 sold
  })

  // Close the modal
  showAddInventoryModal.value = false

  // Show success message (you could add a toast notification here)
  console.log('Product added successfully:', newProduct)
}

// Order Management Methods
const viewOrderDetails = (order) => {
  selectedOrder.value = order
  showOrderDetailsModal.value = true
}

const acceptOrder = async (orderId) => {
  if (processingOrders.value.includes(orderId)) return

  processingOrders.value.push(orderId)

  try {
    await orderService.acceptOrder(orderId)

    // Update the order status in the local data
    const orderIndex = allOrders.value.findIndex(order => order.id === orderId)
    if (orderIndex !== -1) {
      allOrders.value[orderIndex].status = 'Confirmed'
    }

    // Also update recent orders if it exists there
    const recentOrderIndex = recentOrders.value.findIndex(order => order.id === orderId)
    if (recentOrderIndex !== -1) {
      recentOrders.value[recentOrderIndex].status = 'Confirmed'
    }

    console.log('Order accepted successfully')
  } catch (error) {
    console.error('Error accepting order:', error)
    alert('Failed to accept order: ' + (error.message || 'Unknown error'))
  } finally {
    processingOrders.value = processingOrders.value.filter(id => id !== orderId)
  }
}

const acceptOrderFromModal = async (orderId) => {
  await acceptOrder(orderId)
  showOrderDetailsModal.value = false
}

const cancelOrder = async (orderId, reason = '') => {
  if (processingOrders.value.includes(orderId)) return

  processingOrders.value.push(orderId)

  try {
    await orderService.cancelOrder(orderId, reason)

    // Update the order status in the local data
    const orderIndex = allOrders.value.findIndex(order => order.id === orderId)
    if (orderIndex !== -1) {
      allOrders.value[orderIndex].status = 'Cancelled'
    }

    // Also update recent orders if it exists there
    const recentOrderIndex = recentOrders.value.findIndex(order => order.id === orderId)
    if (recentOrderIndex !== -1) {
      recentOrders.value[recentOrderIndex].status = 'Cancelled'
    }

    console.log('Order cancelled successfully')
  } catch (error) {
    console.error('Error cancelling order:', error)
    alert('Failed to cancel order: ' + (error.message || 'Unknown error'))
  } finally {
    processingOrders.value = processingOrders.value.filter(id => id !== orderId)
  }
}

const cancelOrderFromModal = async (data) => {
  await cancelOrder(data.orderId, data.reason)
  showCancelOrderModal.value = false
  showOrderDetailsModal.value = false
}

// Income Statement Methods
const loadIncomeStatement = async () => {
  try {
    // Calculate date range based on selected period
    const endDate = new Date()
    const startDate = new Date()

    if (incomeStatementPeriod.value === 'custom') {
      // For now, just use 30 days for custom
      startDate.setDate(endDate.getDate() - 30)
    } else {
      startDate.setDate(endDate.getDate() - parseInt(incomeStatementPeriod.value))
    }

    const data = await dashboardService.getIncomeStatement(
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0]
    )

    // Update income data with API response
    if (data) {
      incomeData.value = {
        ...incomeData.value,
        ...data
      }
    }
  } catch (error) {
    console.error('Error loading income statement:', error)
    // Keep using mock data if API fails
  }
}

const exportIncomeStatement = () => {
  // Generate and download PDF
  generateIncomeStatementPDF()

  // Show success notification
  console.log('Generating income statement PDF...')
}

const generateIncomeStatementPDF = () => {
  // Create a comprehensive HTML content for the PDF
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  const periodText = getPeriodText(incomeStatementPeriod.value)

  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>Income Statement - ${businessName.value}</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          margin: 0;
          padding: 20px;
          color: #111111;
          line-height: 1.6;
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 3px solid #1E4E79;
          padding-bottom: 20px;
        }
        .company-name {
          font-size: 28px;
          font-weight: bold;
          color: #1E4E79;
          margin-bottom: 5px;
        }
        .report-title {
          font-size: 20px;
          color: #4B4B4B;
          margin-bottom: 10px;
        }
        .period {
          font-size: 14px;
          color: #4B4B4B;
        }
        .summary-cards {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 20px;
          margin-bottom: 30px;
        }
        .summary-card {
          background: #F5F5F5;
          padding: 20px;
          border-radius: 12px;
          text-align: center;
          border-left: 4px solid #1E4E79;
        }
        .summary-card.revenue { border-left-color: #2E7D32; }
        .summary-card.expenses { border-left-color: #FF8F00; }
        .summary-card.profit { border-left-color: #1E4E79; }
        .summary-card h3 {
          margin: 0 0 10px 0;
          font-size: 14px;
          color: #4B4B4B;
          text-transform: uppercase;
          letter-spacing: 1px;
        }
        .summary-card .amount {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .summary-card.revenue .amount { color: #2E7D32; }
        .summary-card.expenses .amount { color: #FF8F00; }
        .summary-card.profit .amount { color: ${incomeData.value.netProfit >= 0 ? '#2E7D32' : '#C62828'}; }
        .breakdown-section {
          margin-bottom: 30px;
        }
        .breakdown-title {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 15px;
          color: #1E4E79;
          border-bottom: 2px solid #1E4E79;
          padding-bottom: 5px;
        }
        .breakdown-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        .breakdown-table th, .breakdown-table td {
          padding: 12px;
          text-align: left;
          border-bottom: 1px solid #CCCCCC;
        }
        .breakdown-table th {
          background: #F5F5F5;
          font-weight: bold;
          color: #1E4E79;
        }
        .breakdown-table .amount {
          text-align: right;
          font-weight: bold;
        }
        .breakdown-table .percentage {
          text-align: right;
          color: #4B4B4B;
          font-size: 12px;
        }
        .footer {
          margin-top: 40px;
          padding-top: 20px;
          border-top: 2px solid #CCCCCC;
          text-align: center;
          color: #4B4B4B;
          font-size: 12px;
        }
        .generated-info {
          background: #EDF6FF;
          padding: 15px;
          border-radius: 8px;
          margin-top: 20px;
          font-size: 12px;
          color: #1E4E79;
        }
        @media print {
          body { margin: 0; }
          .summary-cards { grid-template-columns: repeat(3, 1fr); }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="company-name">${businessName.value}</div>
        <div class="report-title">Income Statement</div>
        <div class="period">${periodText}</div>
      </div>

      <div class="summary-cards">
        <div class="summary-card revenue">
          <h3>Total Revenue</h3>
          <div class="amount">R${incomeData.value.totalRevenue.toLocaleString()}</div>
        </div>
        <div class="summary-card expenses">
          <h3>Total Expenses</h3>
          <div class="amount">R${incomeData.value.totalExpenses.toLocaleString()}</div>
        </div>
        <div class="summary-card profit">
          <h3>Net Profit</h3>
          <div class="amount">R${incomeData.value.netProfit.toLocaleString()}</div>
          <div style="font-size: 12px; margin-top: 5px;">
            ${((incomeData.value.netProfit / incomeData.value.totalRevenue) * 100).toFixed(1)}% margin
          </div>
        </div>
      </div>

      <div class="breakdown-section">
        <div class="breakdown-title">Revenue Breakdown</div>
        <table class="breakdown-table">
          <thead>
            <tr>
              <th>Category</th>
              <th class="amount">Amount</th>
              <th class="percentage">Percentage</th>
            </tr>
          </thead>
          <tbody>
            ${incomeData.value.revenueBreakdown.map(item => `
              <tr>
                <td>${item.category}</td>
                <td class="amount">R${item.amount.toLocaleString()}</td>
                <td class="percentage">${((item.amount / incomeData.value.totalRevenue) * 100).toFixed(1)}%</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>

      <div class="breakdown-section">
        <div class="breakdown-title">Expense Breakdown</div>
        <table class="breakdown-table">
          <thead>
            <tr>
              <th>Category</th>
              <th class="amount">Amount</th>
              <th class="percentage">Percentage</th>
            </tr>
          </thead>
          <tbody>
            ${incomeData.value.expenseBreakdown.map(item => `
              <tr>
                <td>${item.category}</td>
                <td class="amount">R${item.amount.toLocaleString()}</td>
                <td class="percentage">${((item.amount / incomeData.value.totalExpenses) * 100).toFixed(1)}%</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>

      <div class="generated-info">
        <strong>Report Generated:</strong> ${currentDate} at ${new Date().toLocaleTimeString()}<br>
        <strong>Period:</strong> ${periodText}<br>
        <strong>Generated by:</strong> Mthunzi Pod Dashboard
      </div>

      <div class="footer">
        <p>This report was automatically generated by the Mthunzi platform.</p>
        <p>© ${new Date().getFullYear()} NorthForm Production. All rights reserved.</p>
      </div>
    </body>
    </html>
  `

  // Create and download the PDF
  downloadPDF(htmlContent, `Income_Statement_${businessName.value}_${new Date().toISOString().split('T')[0]}.pdf`)
}

const getPeriodText = (period) => {
  switch (period) {
    case '30': return 'Last 30 Days'
    case '90': return 'Last 3 Months'
    case '365': return 'Last Year'
    case 'custom': return 'Custom Period'
    default: return `Last ${period} Days`
  }
}

const downloadPDF = (htmlContent, filename) => {
  // Create a blob with the HTML content
  const blob = new Blob([htmlContent], { type: 'text/html' })
  const url = URL.createObjectURL(blob)

  // Create a temporary link to download the HTML file
  const link = document.createElement('a')
  link.href = url
  link.download = filename.replace('.pdf', '.html')
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  // Also open in new window for printing to PDF
  const printWindow = window.open('', '_blank')
  printWindow.document.write(htmlContent)
  printWindow.document.close()

  // Wait for content to load, then show print dialog
  printWindow.onload = () => {
    printWindow.focus()
    // Give user option to print to PDF
    setTimeout(() => {
      printWindow.print()
    }, 500)
  }

  console.log('Income statement generated successfully. Use browser print dialog to save as PDF.')
}

// Product Management Methods
const editProduct = (product) => {
  selectedProduct.value = product
  showEditProductModal.value = true
}

const handleProductUpdated = async (updatedProduct) => {
  try {
    // In a real app, you'd call the API
    // await inventoryService.updateProduct(updatedProduct.id, updatedProduct)

    // For demo, update local data
    const productIndex = inventory.value.findIndex(p => p.id === updatedProduct.id)
    if (productIndex !== -1) {
      inventory.value[productIndex] = {
        ...inventory.value[productIndex],
        name: updatedProduct.name,
        description: updatedProduct.description,
        price: updatedProduct.price,
        stock: updatedProduct.stock_quantity,
        low_stock_threshold: updatedProduct.low_stock_threshold,
        category: updatedProduct.category,
        status: updatedProduct.status
      }
    }

    console.log('Product updated successfully:', updatedProduct)
    showEditProductModal.value = false
  } catch (error) {
    console.error('Error updating product:', error)
    alert('Failed to update product: ' + (error.message || 'Unknown error'))
  }
}

const openImageUploadFromEdit = () => {
  showEditProductModal.value = false
  showImageUploadModal.value = true
}

const restockProduct = (product) => {
  selectedProduct.value = product
  showRestockModal.value = true
}

const restockProductFromModal = async (data) => {
  isProcessingRestock.value = true

  try {
    // In a real app, you'd call the API
    // await inventoryService.restockProduct(data.productId, data.quantity, data.costPerUnit, data.notes)

    // For demo, update local data
    const productIndex = inventory.value.findIndex(p => p.id === data.productId)
    if (productIndex !== -1) {
      inventory.value[productIndex].stock += data.quantity
    }

    console.log('Product restocked successfully:', data)
    showRestockModal.value = false
  } catch (error) {
    console.error('Error restocking product:', error)
    alert('Failed to restock product: ' + (error.message || 'Unknown error'))
  } finally {
    isProcessingRestock.value = false
  }
}

const uploadProductImages = (product) => {
  selectedProduct.value = product
  showImageUploadModal.value = true
}

const handleImagesUpdated = (data) => {
  const productIndex = inventory.value.findIndex(p => p.id === selectedProduct.value?.id)
  if (productIndex === -1) return

  if (data.action === 'add') {
    if (!inventory.value[productIndex].images) {
      inventory.value[productIndex].images = []
    }
    inventory.value[productIndex].images.push(...data.images)
  } else if (data.action === 'remove') {
    if (inventory.value[productIndex].images) {
      inventory.value[productIndex].images.splice(data.imageIndex, 1)
    }
  }

  console.log('Images updated for product:', selectedProduct.value?.name)
}

// Close user menu when clicking outside
const handleClickOutside = (event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target)) {
    showUserMenu.value = false
  }
}

onMounted(async () => {
  console.log('Pod Dashboard loaded')
  document.addEventListener('click', handleClickOutside)

  // Load all dashboard data
  await loadAllData()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
